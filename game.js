class BreakoutGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.scoreElement = document.getElementById('score');
        this.livesElement = document.getElementById('lives');
        this.levelElement = document.getElementById('level');
        this.gameOverDiv = document.getElementById('gameOver');
        this.finalScoreElement = document.getElementById('finalScore');
        
        // 遊戲狀態
        this.gameState = 'waiting'; // waiting, playing, paused, gameOver
        this.animationId = null;
        
        // 遊戲數據
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        
        // 球的屬性
        this.ball = {
            x: this.canvas.width / 2,
            y: this.canvas.height - 50,
            dx: 0,
            dy: 0,
            radius: 10,
            speed: 5
        };
        
        // 擋板屬性
        this.paddle = {
            x: this.canvas.width / 2 - 60,
            y: this.canvas.height - 20,
            width: 120,
            height: 15,
            speed: 8
        };
        
        // 磚塊屬性
        this.bricks = [];
        this.brickRows = 5;
        this.brickCols = 10;
        this.brickWidth = 70;
        this.brickHeight = 20;
        this.brickPadding = 5;
        this.brickOffsetTop = 60;
        this.brickOffsetLeft = 35;
        
        // 按鍵狀態
        this.keys = {};
        
        this.initializeGame();
        this.setupEventListeners();
    }
    
    initializeGame() {
        this.createBricks();
        this.resetBall();
    }
    
    createBricks() {
        this.bricks = [];
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
        
        for (let r = 0; r < this.brickRows; r++) {
            for (let c = 0; c < this.brickCols; c++) {
                this.bricks.push({
                    x: c * (this.brickWidth + this.brickPadding) + this.brickOffsetLeft,
                    y: r * (this.brickHeight + this.brickPadding) + this.brickOffsetTop,
                    width: this.brickWidth,
                    height: this.brickHeight,
                    color: colors[r % colors.length],
                    visible: true,
                    points: (this.brickRows - r) * 10
                });
            }
        }
    }
    
    resetBall() {
        this.ball.x = this.canvas.width / 2;
        this.ball.y = this.canvas.height - 50;
        this.ball.dx = 0;
        this.ball.dy = 0;
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            this.keys[e.key] = true;
            
            if (e.key === ' ' && this.gameState === 'playing' && this.ball.dx === 0 && this.ball.dy === 0) {
                this.launchBall();
            }
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.key] = false;
        });
    }
    
    launchBall() {
        const angle = (Math.random() - 0.5) * Math.PI / 3; // -30度到30度
        this.ball.dx = Math.sin(angle) * this.ball.speed;
        this.ball.dy = -Math.cos(angle) * this.ball.speed;
    }
    
    startGame() {
        if (this.gameState === 'waiting' || this.gameState === 'gameOver') {
            this.gameState = 'playing';
            this.gameLoop();
        }
    }
    
    pauseGame() {
        if (this.gameState === 'playing') {
            this.gameState = 'paused';
            cancelAnimationFrame(this.animationId);
        } else if (this.gameState === 'paused') {
            this.gameState = 'playing';
            this.gameLoop();
        }
    }
    
    resetGame() {
        this.gameState = 'waiting';
        this.score = 0;
        this.lives = 3;
        this.level = 1;
        this.gameOverDiv.style.display = 'none';
        
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        this.initializeGame();
        this.updateUI();
        this.draw();
    }
    
    updateUI() {
        this.scoreElement.textContent = this.score;
        this.livesElement.textContent = this.lives;
        this.levelElement.textContent = this.level;
    }
    
    gameLoop() {
        if (this.gameState !== 'playing') return;
        
        this.update();
        this.draw();
        this.animationId = requestAnimationFrame(() => this.gameLoop());
    }
    
    update() {
        // 更新擋板位置
        if ((this.keys['ArrowLeft'] || this.keys['a'] || this.keys['A']) && this.paddle.x > 0) {
            this.paddle.x -= this.paddle.speed;
        }
        if ((this.keys['ArrowRight'] || this.keys['d'] || this.keys['D']) && 
            this.paddle.x < this.canvas.width - this.paddle.width) {
            this.paddle.x += this.paddle.speed;
        }
        
        // 更新球的位置
        if (this.ball.dx !== 0 || this.ball.dy !== 0) {
            this.ball.x += this.ball.dx;
            this.ball.y += this.ball.dy;
            
            // 球與牆壁碰撞
            if (this.ball.x + this.ball.radius > this.canvas.width || this.ball.x - this.ball.radius < 0) {
                this.ball.dx = -this.ball.dx;
            }
            if (this.ball.y - this.ball.radius < 0) {
                this.ball.dy = -this.ball.dy;
            }
            
            // 球掉落
            if (this.ball.y + this.ball.radius > this.canvas.height) {
                this.lives--;
                if (this.lives <= 0) {
                    this.endGame();
                } else {
                    this.resetBall();
                }
            }
            
            // 球與擋板碰撞
            this.checkPaddleCollision();
            
            // 球與磚塊碰撞
            this.checkBrickCollisions();
            
            // 檢查是否完成關卡
            if (this.bricks.every(brick => !brick.visible)) {
                this.nextLevel();
            }
        }
        
        this.updateUI();
    }

    checkPaddleCollision() {
        if (this.ball.y + this.ball.radius > this.paddle.y &&
            this.ball.x > this.paddle.x &&
            this.ball.x < this.paddle.x + this.paddle.width) {

            // 計算球撞擊擋板的位置
            const hitPos = (this.ball.x - this.paddle.x) / this.paddle.width;
            const angle = (hitPos - 0.5) * Math.PI / 3; // -60度到60度

            this.ball.dx = Math.sin(angle) * this.ball.speed;
            this.ball.dy = -Math.abs(Math.cos(angle) * this.ball.speed);

            // 確保球在擋板上方
            this.ball.y = this.paddle.y - this.ball.radius;
        }
    }

    checkBrickCollisions() {
        for (let brick of this.bricks) {
            if (!brick.visible) continue;

            if (this.ball.x + this.ball.radius > brick.x &&
                this.ball.x - this.ball.radius < brick.x + brick.width &&
                this.ball.y + this.ball.radius > brick.y &&
                this.ball.y - this.ball.radius < brick.y + brick.height) {

                brick.visible = false;
                this.score += brick.points;

                // 簡單的碰撞反彈
                this.ball.dy = -this.ball.dy;
                break;
            }
        }
    }

    nextLevel() {
        this.level++;
        this.ball.speed += 0.5; // 增加球速
        this.createBricks();
        this.resetBall();
    }

    endGame() {
        this.gameState = 'gameOver';
        this.finalScoreElement.textContent = this.score;
        this.gameOverDiv.style.display = 'block';

        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }

    draw() {
        // 清除畫布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 繪製科幻背景
        this.drawCyberBackground();

        // 繪製磚塊
        this.drawBricks();

        // 繪製球
        this.drawBall();

        // 繪製擋板
        this.drawPaddle();

        // 繪製UI效果
        this.drawUI();
    }

    drawCyberBackground() {
        // 深色漸變背景
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#0a0a0a');
        gradient.addColorStop(0.5, '#1a1a2e');
        gradient.addColorStop(1, '#16213e');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 網格線效果
        this.ctx.strokeStyle = 'rgba(0, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;

        // 垂直線
        for (let x = 0; x < this.canvas.width; x += 40) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }

        // 水平線
        for (let y = 0; y < this.canvas.height; y += 40) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    drawBricks() {
        for (let brick of this.bricks) {
            if (brick.visible) {
                // 霓虹發光效果
                this.ctx.shadowColor = brick.color;
                this.ctx.shadowBlur = 15;
                this.ctx.fillStyle = brick.color;
                this.ctx.fillRect(brick.x, brick.y, brick.width, brick.height);

                // 內部漸變
                const brickGradient = this.ctx.createLinearGradient(
                    brick.x, brick.y, brick.x, brick.y + brick.height
                );
                brickGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
                brickGradient.addColorStop(1, 'rgba(0, 0, 0, 0.3)');
                this.ctx.fillStyle = brickGradient;
                this.ctx.fillRect(brick.x, brick.y, brick.width, brick.height);

                // 邊框發光
                this.ctx.strokeStyle = '#00ffff';
                this.ctx.lineWidth = 2;
                this.ctx.strokeRect(brick.x, brick.y, brick.width, brick.height);

                // 重置陰影
                this.ctx.shadowBlur = 0;
            }
        }
    }

    drawBall() {
        // 球的發光效果
        this.ctx.shadowColor = '#00ffff';
        this.ctx.shadowBlur = 20;

        // 外圈發光
        this.ctx.beginPath();
        this.ctx.arc(this.ball.x, this.ball.y, this.ball.radius + 5, 0, Math.PI * 2);
        this.ctx.fillStyle = 'rgba(0, 255, 255, 0.3)';
        this.ctx.fill();

        // 主球體
        this.ctx.beginPath();
        this.ctx.arc(this.ball.x, this.ball.y, this.ball.radius, 0, Math.PI * 2);

        // 球的漸變
        const ballGradient = this.ctx.createRadialGradient(
            this.ball.x - 3, this.ball.y - 3, 0,
            this.ball.x, this.ball.y, this.ball.radius
        );
        ballGradient.addColorStop(0, '#ffffff');
        ballGradient.addColorStop(0.3, '#00ffff');
        ballGradient.addColorStop(1, '#0080ff');

        this.ctx.fillStyle = ballGradient;
        this.ctx.fill();

        // 球的邊框
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();

        // 重置陰影
        this.ctx.shadowBlur = 0;
    }

    drawPaddle() {
        // 擋板發光效果
        this.ctx.shadowColor = '#ff0080';
        this.ctx.shadowBlur = 15;

        // 擋板漸變
        const paddleGradient = this.ctx.createLinearGradient(
            this.paddle.x, this.paddle.y,
            this.paddle.x, this.paddle.y + this.paddle.height
        );
        paddleGradient.addColorStop(0, '#ff0080');
        paddleGradient.addColorStop(0.5, '#ff4080');
        paddleGradient.addColorStop(1, '#800040');

        this.ctx.fillStyle = paddleGradient;
        this.ctx.fillRect(this.paddle.x, this.paddle.y, this.paddle.width, this.paddle.height);

        // 擋板邊框
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(this.paddle.x, this.paddle.y, this.paddle.width, this.paddle.height);

        // 中央發光線
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.moveTo(this.paddle.x + this.paddle.width / 2, this.paddle.y);
        this.ctx.lineTo(this.paddle.x + this.paddle.width / 2, this.paddle.y + this.paddle.height);
        this.ctx.stroke();

        // 重置陰影
        this.ctx.shadowBlur = 0;
    }

    drawUI() {
        // 繪製遊戲狀態文字
        this.ctx.font = '28px "Courier New", monospace';
        this.ctx.textAlign = 'center';

        if (this.gameState === 'waiting') {
            // 發光文字效果
            this.ctx.shadowColor = '#00ffff';
            this.ctx.shadowBlur = 10;
            this.ctx.fillStyle = '#00ffff';
            this.ctx.fillText('>>> 按空白鍵開始遊戲 <<<', this.canvas.width / 2, this.canvas.height / 2);

            // 閃爍效果
            const time = Date.now() * 0.005;
            const alpha = (Math.sin(time) + 1) * 0.5;
            this.ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
            this.ctx.fillText('>>> 按空白鍵開始遊戲 <<<', this.canvas.width / 2, this.canvas.height / 2);

        } else if (this.gameState === 'paused') {
            this.ctx.shadowColor = '#ff8000';
            this.ctx.shadowBlur = 10;
            this.ctx.fillStyle = '#ff8000';
            this.ctx.fillText('=== 遊戲暫停 ===', this.canvas.width / 2, this.canvas.height / 2);
        }

        // 如果球還沒發射，顯示提示
        if (this.gameState === 'playing' && this.ball.dx === 0 && this.ball.dy === 0) {
            this.ctx.font = '18px "Courier New", monospace';
            this.ctx.shadowColor = '#ffffff';
            this.ctx.shadowBlur = 5;
            this.ctx.fillStyle = '#ffffff';
            this.ctx.fillText('[ 按空白鍵發射球 ]', this.canvas.width / 2, this.canvas.height - 100);
        }

        // 重置陰影
        this.ctx.shadowBlur = 0;
    }
}

// 創建遊戲實例
const game = new BreakoutGame();
