<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打磚塊遊戲</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 20px;
            background:
                radial-gradient(circle at 20% 80%, #120458 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, #421a78 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, #0f0f23 0%, transparent 50%),
                linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            font-family: 'Orbitron', monospace;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                repeating-linear-gradient(
                    90deg,
                    transparent,
                    transparent 2px,
                    rgba(0, 255, 255, 0.03) 2px,
                    rgba(0, 255, 255, 0.03) 4px
                ),
                repeating-linear-gradient(
                    0deg,
                    transparent,
                    transparent 2px,
                    rgba(255, 0, 128, 0.03) 2px,
                    rgba(255, 0, 128, 0.03) 4px
                );
            pointer-events: none;
            z-index: 1;
        }

        h1 {
            color: #00ffff;
            text-shadow:
                0 0 5px #00ffff,
                0 0 10px #00ffff,
                0 0 15px #00ffff,
                0 0 20px #00ffff;
            margin-bottom: 30px;
            font-weight: 900;
            font-size: 3em;
            letter-spacing: 3px;
            animation: glow 2s ease-in-out infinite alternate;
            z-index: 2;
            position: relative;
        }

        @keyframes glow {
            from {
                text-shadow:
                    0 0 5px #00ffff,
                    0 0 10px #00ffff,
                    0 0 15px #00ffff,
                    0 0 20px #00ffff;
            }
            to {
                text-shadow:
                    0 0 10px #00ffff,
                    0 0 20px #00ffff,
                    0 0 30px #00ffff,
                    0 0 40px #00ffff;
            }
        }

        #gameContainer {
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00ffff;
            border-radius: 15px;
            padding: 25px;
            box-shadow:
                0 0 20px rgba(0, 255, 255, 0.5),
                inset 0 0 20px rgba(0, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            z-index: 2;
            position: relative;
        }

        #gameCanvas {
            border: 3px solid #ff0080;
            border-radius: 10px;
            display: block;
            box-shadow:
                0 0 30px rgba(255, 0, 128, 0.6),
                inset 0 0 30px rgba(255, 0, 128, 0.1);
        }

        #gameInfo {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
            font-size: 20px;
            font-weight: 700;
            color: #00ffff;
            text-shadow: 0 0 10px #00ffff;
            font-family: 'Orbitron', monospace;
        }

        #controls {
            margin-top: 30px;
            text-align: center;
            color: #ffffff;
            z-index: 2;
            position: relative;
        }

        #controls p {
            font-family: 'Orbitron', monospace;
            font-size: 14px;
            margin: 10px 0;
            text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
        }

        .button {
            background: linear-gradient(45deg, #ff0080, #8000ff);
            color: white;
            border: 2px solid #00ffff;
            padding: 12px 25px;
            font-size: 16px;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow:
                0 0 15px rgba(255, 0, 128, 0.5),
                inset 0 0 15px rgba(255, 255, 255, 0.1);
        }

        .button:hover {
            background: linear-gradient(45deg, #ff4080, #a040ff);
            box-shadow:
                0 0 25px rgba(255, 0, 128, 0.8),
                inset 0 0 25px rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .button:active {
            transform: translateY(0);
        }

        .button:disabled {
            background: #333333;
            border-color: #666666;
            cursor: not-allowed;
            box-shadow: none;
        }

        #gameOver {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.95);
            border: 3px solid #ff0080;
            color: #00ffff;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            display: none;
            box-shadow:
                0 0 50px rgba(255, 0, 128, 0.8),
                inset 0 0 30px rgba(0, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            z-index: 1000;
        }

        #gameOver h2 {
            color: #ff0080;
            text-shadow: 0 0 15px #ff0080;
            margin-bottom: 20px;
            font-family: 'Orbitron', monospace;
        }
    </style>
</head>
<body>
    <h1>◢ CYBER BREAKOUT ◣</h1>

    <div id="gameContainer">
        <canvas id="gameCanvas" width="800" height="600"></canvas>

        <div id="gameInfo">
            <div>SCORE: <span id="score">0</span></div>
            <div>LIVES: <span id="lives">3</span></div>
            <div>LEVEL: <span id="level">1</span></div>
        </div>
    </div>

    <div id="controls">
        <p>◄ ► 方向鍵 或 A D 鍵控制擋板</p>
        <p>[ SPACE ] 發射能量球</p>
        <button class="button" onclick="game.startGame()">啟動系統</button>
        <button class="button" onclick="game.pauseGame()">暫停/繼續</button>
        <button class="button" onclick="game.resetGame()">重置系統</button>
    </div>

    <div id="gameOver">
        <h2 id="gameOverTitle">SYSTEM TERMINATED</h2>
        <p id="gameOverMessage">FINAL SCORE: <span id="finalScore">0</span></p>
        <button class="button" onclick="game.resetGame()">RESTART SYSTEM</button>
    </div>

    <script src="game.js"></script>
</body>
</html>
